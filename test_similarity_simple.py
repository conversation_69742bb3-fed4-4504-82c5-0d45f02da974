#!/usr/bin/env python3
"""
简单测试两个文本之间的相似度分数
"""

import asyncio
import os
import sys
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


async def calculate_similarity(text1, text2):
    """计算两个文本的相似度"""
    try:
        from core.common.llm_client import create_embedding_function
        
        # 创建embedding函数
        embedding_func = await create_embedding_function()
        if not embedding_func:
            print("❌ 无法创建embedding函数")
            return None
        
        # 生成embeddings
        embeddings = await embedding_func([text1, text2])
        if not embeddings or len(embeddings) != 2:
            print("❌ 向量化失败")
            return None
        
        # 转换为numpy数组
        emb1 = np.array(embeddings[0])
        emb2 = np.array(embeddings[1])
        
        # 计算余弦相似度
        dot_product = np.dot(emb1, emb2)
        norm1 = np.linalg.norm(emb1)
        norm2 = np.linalg.norm(emb2)
        cosine_similarity = dot_product / (norm1 * norm2)
        
        # 计算距离（NanoVectorDB使用的）
        distance = 1.0 - cosine_similarity
        
        return {
            "cosine_similarity": float(cosine_similarity),
            "distance": float(distance),
            "norm1": float(norm1),
            "norm2": float(norm2),
            "dot_product": float(dot_product)
        }
        
    except Exception as e:
        print(f"❌ 计算相似度失败: {e}")
        return None


async def test_similarity_pairs():
    """测试多组文本对的相似度"""
    
    test_pairs = [
        # 完全相同的文本
        ("问答系统支持哪些功能？", "问答系统支持哪些功能？"),
        
        # 非常相似的文本
        ("问答系统支持哪些功能？", "问答系统有哪些功能？"),
        
        # 相似但不同的文本
        ("问答系统支持哪些功能？", "问答系统具备什么功能？"),
        
        # 相关但不太相似的文本
        ("问答系统支持哪些功能？", "如何使用问答系统？"),
        
        # 完全不同的文本
        ("问答系统支持哪些功能？", "什么是机器学习？"),
        
        # 另一组完全相同的文本
        ("什么是GuiXiaoXiRag？", "什么是GuiXiaoXiRag？"),
        
        # 系统中的其他问题
        ("如何提高问答匹配的准确性？", "如何提高问答匹配的准确性？"),
        ("如何使用问答系统？", "如何使用问答系统？"),
    ]
    
    print("🔍 文本相似度测试")
    print("=" * 80)
    
    for i, (text1, text2) in enumerate(test_pairs, 1):
        print(f"\n测试 {i}:")
        print(f"  文本1: '{text1}'")
        print(f"  文本2: '{text2}'")
        
        result = await calculate_similarity(text1, text2)
        
        if result:
            similarity = result["cosine_similarity"]
            distance = result["distance"]
            
            print(f"  📊 结果:")
            print(f"    余弦相似度: {similarity:.8f}")
            print(f"    距离: {distance:.8f}")
            print(f"    向量1范数: {result['norm1']:.6f}")
            print(f"    向量2范数: {result['norm2']:.6f}")
            print(f"    点积: {result['dot_product']:.6f}")
            
            # 判断相似度等级
            if text1 == text2:
                if similarity > 0.999:
                    print(f"    ✅ 完全相同文本的相似度正常")
                elif similarity > 0.99:
                    print(f"    ⚠️  完全相同文本的相似度稍低")
                else:
                    print(f"    ❌ 完全相同文本的相似度异常低")
            else:
                if similarity > 0.9:
                    print(f"    📈 高相似度")
                elif similarity > 0.7:
                    print(f"    📊 中等相似度")
                elif similarity > 0.5:
                    print(f"    📉 低相似度")
                else:
                    print(f"    📋 很低相似度")
        else:
            print(f"  ❌ 计算失败")


async def interactive_test():
    """交互式测试"""
    print("\n" + "=" * 80)
    print("🎯 交互式相似度测试")
    print("输入两个文本来测试它们的相似度，输入 'quit' 退出")
    print("=" * 80)
    
    while True:
        try:
            print("\n请输入第一个文本:")
            text1 = input("> ").strip()
            if text1.lower() == 'quit':
                break
            
            print("请输入第二个文本:")
            text2 = input("> ").strip()
            if text2.lower() == 'quit':
                break
            
            if not text1 or not text2:
                print("❌ 文本不能为空")
                continue
            
            print(f"\n计算相似度...")
            result = await calculate_similarity(text1, text2)
            
            if result:
                similarity = result["cosine_similarity"]
                distance = result["distance"]
                
                print(f"\n📊 相似度结果:")
                print(f"  文本1: '{text1}'")
                print(f"  文本2: '{text2}'")
                print(f"  余弦相似度: {similarity:.8f}")
                print(f"  距离: {distance:.8f}")
                
                if text1 == text2:
                    print(f"  💡 这是完全相同的文本")
                elif similarity > 0.9:
                    print(f"  💡 这两个文本非常相似")
                elif similarity > 0.7:
                    print(f"  💡 这两个文本比较相似")
                elif similarity > 0.5:
                    print(f"  💡 这两个文本有一定相似性")
                else:
                    print(f"  💡 这两个文本相似度较低")
            else:
                print(f"❌ 计算失败")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("\n👋 测试结束")


async def main():
    """主函数"""
    print("🔧 简单文本相似度测试工具\n")
    
    # 预设测试
    await test_similarity_pairs()
    
    # 交互式测试
    try:
        await interactive_test()
    except KeyboardInterrupt:
        print("\n👋 程序结束")


if __name__ == "__main__":
    asyncio.run(main())
