#!/usr/bin/env python3
"""
检查向量存储的实际内容
"""

import json
import os
import sys
import asyncio
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def check_vector_files():
    """检查向量文件的内容"""
    print("🔍 检查向量文件内容")
    print("=" * 60)
    
    base_path = "./Q_A_Base"
    
    for category in ["features", "optimization", "system", "usage"]:
        category_path = os.path.join(base_path, category)
        
        # 检查问答对文件
        qa_file = os.path.join(category_path, "qa_pairs_default.json")
        vdb_file = os.path.join(category_path, "qa_vdb_default.json")
        
        print(f"\n📁 分类: {category}")
        
        if os.path.exists(qa_file):
            try:
                with open(qa_file, 'r', encoding='utf-8') as f:
                    qa_data = json.load(f)
                
                qa_pairs = qa_data.get('qa_pairs', [])
                print(f"  📄 问答对文件: {len(qa_pairs)} 条记录")
                
                for i, qa in enumerate(qa_pairs):
                    print(f"    {i+1}. ID: {qa.get('id')}")
                    print(f"       问题: '{qa.get('question')}'")
                    print(f"       答案: '{qa.get('answer')[:30]}...'")
                    
            except Exception as e:
                print(f"  ❌ 读取问答对文件失败: {e}")
        else:
            print(f"  ❌ 问答对文件不存在")
        
        if os.path.exists(vdb_file):
            try:
                with open(vdb_file, 'r', encoding='utf-8') as f:
                    vdb_data = json.load(f)
                
                vector_data = vdb_data.get('data', [])
                matrix = vdb_data.get('matrix', [])
                
                print(f"  🗃️  向量文件: {len(vector_data)} 条记录, 向量矩阵: {len(matrix)} 个向量")
                
                for i, vec in enumerate(vector_data):
                    print(f"    {i+1}. ID: {vec.get('__id__')}")
                    print(f"       问题: '{vec.get('question', 'N/A')}'")
                    print(f"       内容: '{vec.get('content', 'N/A')}'")
                    print(f"       答案: '{vec.get('answer', 'N/A')[:30]}...'")
                    
                    # 检查content字段是否与question一致
                    question = vec.get('question', '')
                    content = vec.get('content', '')
                    if question and content:
                        if question == content:
                            print(f"       ✅ content字段与question一致")
                        else:
                            print(f"       ❌ content字段与question不一致")
                            print(f"          question: '{question}'")
                            print(f"          content:  '{content}'")
                    else:
                        print(f"       ⚠️  缺少question或content字段")
                    
            except Exception as e:
                print(f"  ❌ 读取向量文件失败: {e}")
        else:
            print(f"  ❌ 向量文件不存在")


async def test_direct_vector_query():
    """直接测试向量查询"""
    print("\n" + "=" * 60)
    print("🎯 直接测试向量查询")
    print("=" * 60)
    
    try:
        from core.common.llm_client import create_embedding_function
        from nano_vectordb import NanoVectorDB
        
        embedding_func = await create_embedding_function()
        
        # 测试每个分类的向量数据库
        base_path = "./Q_A_Base"
        
        for category in ["features", "optimization", "system", "usage"]:
            category_path = os.path.join(base_path, category)
            vdb_file = os.path.join(category_path, "qa_vdb_default.json")
            
            if not os.path.exists(vdb_file):
                print(f"\n❌ 分类 '{category}': 向量文件不存在")
                continue
            
            print(f"\n📁 测试分类: {category}")
            
            # 加载向量数据库
            db = NanoVectorDB(
                embedding_dim=2560,
                metric="cosine",
                storage_file=vdb_file
            )
            
            # 读取向量文件获取存储的问题
            with open(vdb_file, 'r', encoding='utf-8') as f:
                vdb_data = json.load(f)
            
            vector_data = vdb_data.get('data', [])
            
            for vec_item in vector_data:
                stored_question = vec_item.get('question', '')
                stored_content = vec_item.get('content', '')
                
                if not stored_question:
                    continue
                
                print(f"\n  🔍 测试问题: '{stored_question}'")
                print(f"     存储内容: '{stored_content}'")
                
                # 生成查询向量
                query_embedding = await embedding_func([stored_question])
                if not query_embedding:
                    print(f"     ❌ 查询向量生成失败")
                    continue
                
                query_vector = np.array(query_embedding[0], dtype=np.float32)
                
                # 执行查询
                results = db.query(query_vector=query_vector, top_k=3)
                
                if results:
                    print(f"     📊 查询结果:")
                    for i, result in enumerate(results):
                        distance = result.get("__distance__", 1.0)
                        similarity = 1.0 - distance
                        result_question = result.get("question", "N/A")
                        result_content = result.get("content", "N/A")
                        
                        print(f"       {i+1}. 距离={distance:.6f}, 相似度={similarity:.6f}")
                        print(f"          问题: '{result_question}'")
                        print(f"          内容: '{result_content}'")
                        
                        # 检查是否匹配到自己
                        if stored_question == result_question:
                            if similarity > 0.99:
                                print(f"          ✅ 自匹配相似度正常")
                            else:
                                print(f"          ❌ 自匹配相似度异常: {similarity:.6f}")
                        else:
                            print(f"          ⚠️  匹配到不同问题")
                else:
                    print(f"     ❌ 查询无结果")
        
    except Exception as e:
        print(f"❌ 直接向量查询测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_qa_manager_query():
    """测试QA管理器的查询"""
    print("\n" + "=" * 60)
    print("🎯 测试QA管理器查询")
    print("=" * 60)
    
    try:
        from core.quick_qa_base.optimized_qa_manager import OptimizedQAManager
        
        qa_manager = OptimizedQAManager(
            workspace="qa_base",
            namespace="default",
            similarity_threshold=0.1,
            working_dir="./Q_A_Base"
        )
        
        success = await qa_manager.initialize()
        if not success:
            print("❌ QA管理器初始化失败")
            return
        
        print("✅ QA管理器初始化成功")
        
        # 获取所有问答对
        qa_pairs = qa_manager.storage.qa_pairs
        print(f"📊 系统中共有 {len(qa_pairs)} 个问答对")
        
        # 测试每个问题
        for qa_id, qa_pair in list(qa_pairs.items())[:4]:  # 只测试前4个
            question = qa_pair.question
            
            print(f"\n🔍 测试问题: '{question}'")
            print(f"   QA ID: {qa_id}")
            print(f"   分类: {qa_pair.category}")
            
            # 使用QA管理器查询
            result = await qa_manager.query(
                question=question,
                top_k=3,
                min_similarity=0.0
            )
            
            if result.get("found"):
                print(f"   ✅ 找到匹配")
                print(f"   最佳匹配:")
                print(f"     问题: '{result.get('question')}'")
                print(f"     相似度: {result.get('similarity', 0):.6f}")
                print(f"     分类: {result.get('category')}")
                print(f"     QA ID: {result.get('qa_id')}")
                
                # 检查是否匹配到自己
                if question == result.get('question'):
                    similarity = result.get('similarity', 0)
                    if similarity > 0.99:
                        print(f"     ✅ 自匹配相似度正常")
                    else:
                        print(f"     ❌ 自匹配相似度异常: {similarity:.6f}")
                else:
                    print(f"     ⚠️  匹配到不同问题")
                
                # 显示所有结果
                all_results = result.get('all_results', [])
                if len(all_results) > 1:
                    print(f"   📋 所有结果:")
                    for i, res in enumerate(all_results):
                        qa_pair_res = res.get('qa_pair', {})
                        sim = res.get('similarity', 0)
                        print(f"     {i+1}. '{qa_pair_res.get('question', 'N/A')}' - 相似度: {sim:.6f}")
            else:
                print(f"   ❌ 未找到匹配")
        
        await qa_manager.cleanup()
        
    except Exception as e:
        print(f"❌ QA管理器测试失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    print("🔧 向量存储内容检查工具")
    
    # 检查向量文件内容
    check_vector_files()
    
    # 直接测试向量查询
    await test_direct_vector_query()
    
    # 测试QA管理器查询
    await test_qa_manager_query()
    
    print("\n" + "=" * 60)
    print("📋 检查完成")


if __name__ == "__main__":
    asyncio.run(main())
