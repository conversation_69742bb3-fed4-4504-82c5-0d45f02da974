#!/usr/bin/env python3
"""
基于RAG系统实现修复问答系统的最终脚本
"""

import json
import os
import shutil
import time


def backup_data():
    """备份数据"""
    print("=== 备份数据 ===\n")
    
    base_path = "./Q_A_Base"
    backup_path = f"./Q_A_Base_backup_rag_fix_{int(time.time())}"
    
    if os.path.exists(base_path):
        try:
            shutil.copytree(base_path, backup_path)
            print(f"✅ 数据已备份到: {backup_path}")
            return backup_path
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return None
    else:
        print("❌ Q_A_Base目录不存在")
        return None


def create_clean_qa_data():
    """创建干净的问答数据（每个分类只有一个唯一问答对）"""
    print("\n=== 创建干净的问答数据 ===\n")
    
    base_path = "./Q_A_Base"
    
    # 删除整个目录重新创建
    if os.path.exists(base_path):
        shutil.rmtree(base_path)
    
    # 定义每个分类的唯一问答对
    unique_qa_data = {
        "features": {
            "id": "qa_features_001",
            "question": "问答系统支持哪些功能？",
            "answer": "问答系统支持：1) 智能问答查询；2) 问答对的增删改查；3) 批量导入导出；4) 相似度搜索；5) 分类管理；6) 统计分析；7) 数据备份恢复等功能。",
            "category": "features",
            "confidence": 1.0,
            "keywords": [],
            "source": "system_default"
        },
        "optimization": {
            "id": "qa_optimization_001", 
            "question": "如何提高问答匹配的准确性？",
            "answer": "提高匹配准确性的方法：1) 添加更多高质量的问答对；2) 优化问题的表述，使用清晰准确的语言；3) 合理设置相似度阈值；4) 为问答对添加相关关键词；5) 定期更新和维护问答库。",
            "category": "optimization",
            "confidence": 0.9,
            "keywords": [],
            "source": "system_default"
        },
        "system": {
            "id": "qa_system_001",
            "question": "什么是GuiXiaoXiRag？", 
            "answer": "GuiXiaoXiRag是一个基于FastAPI的智能知识问答系统，集成了知识图谱、向量检索、意图识别等多种AI技术，提供强大的知识管理和智能查询功能。",
            "category": "system",
            "confidence": 1.0,
            "keywords": [],
            "source": "system_default"
        },
        "usage": {
            "id": "qa_usage_001",
            "question": "如何使用问答系统？",
            "answer": "您可以通过API接口向问答系统提交问题，系统会使用向量相似度匹配找到最相关的答案。支持单个查询和批量查询，还可以设置相似度阈值来控制匹配精度。",
            "category": "usage", 
            "confidence": 0.9,
            "keywords": [],
            "source": "system_default"
        }
    }
    
    current_time = time.time()
    
    for category, qa_data in unique_qa_data.items():
        category_path = os.path.join(base_path, category)
        os.makedirs(category_path, exist_ok=True)
        
        # 添加时间戳
        qa_data["created_at"] = current_time
        qa_data["updated_at"] = current_time
        
        # 创建问答对文件（只有一个问答对）
        qa_file = os.path.join(category_path, "qa_pairs_default.json")
        qa_file_data = {
            "namespace": "default",
            "category": category,
            "qa_pairs": [qa_data],  # 只有一个唯一的问答对
            "metadata": {
                "total_pairs": 1,
                "created_at": current_time,
                "updated_at": current_time
            }
        }
        
        try:
            with open(qa_file, 'w', encoding='utf-8') as f:
                json.dump(qa_file_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 分类 '{category}': 创建唯一问答对")
            print(f"   ID: {qa_data['id']}")
            print(f"   问题: {qa_data['question']}")
            
        except Exception as e:
            print(f"❌ 分类 '{category}': 创建失败 - {e}")
            return False
    
    print(f"\n✅ 所有分类数据创建完成")
    return True


def verify_clean_data():
    """验证清理后的数据"""
    print("\n=== 验证清理后的数据 ===\n")
    
    base_path = "./Q_A_Base"
    
    for category in ["features", "optimization", "system", "usage"]:
        category_path = os.path.join(base_path, category)
        qa_file = os.path.join(category_path, "qa_pairs_default.json")
        vdb_file = os.path.join(category_path, "qa_vdb_default.json")
        
        print(f"分类 '{category}':")
        
        # 检查问答对文件
        if os.path.exists(qa_file):
            try:
                with open(qa_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                qa_pairs = data.get('qa_pairs', [])
                print(f"  ✅ 问答对文件: {len(qa_pairs)} 个问答对")
                
                if qa_pairs:
                    qa = qa_pairs[0]
                    print(f"     ID: {qa.get('id')}")
                    print(f"     问题: {qa.get('question')}")
                
            except Exception as e:
                print(f"  ❌ 问答对文件验证失败: {e}")
        else:
            print(f"  ❌ 问答对文件不存在")
        
        # 检查向量文件（应该不存在）
        if os.path.exists(vdb_file):
            print(f"  ⚠️  向量文件存在，将在服务器启动时重建")
        else:
            print(f"  ✅ 向量文件不存在（将在服务器启动时创建）")
        
        print()


def main():
    """主函数"""
    print("🔧 基于RAG系统实现修复问答系统")
    print("=" * 60)
    
    print("修复内容:")
    print("  ✅ 参考RAG系统的向量存储实现")
    print("  ✅ 修复upsert方法的embedding处理")
    print("  ✅ 修复query方法的结果处理")
    print("  ✅ 添加正确的相似度计算")
    print("  ✅ 确保meta_fields包含content字段")
    print("  ✅ 清理重复数据问题")
    print()
    
    print("问题分析:")
    print("  ❌ 每个分类有多个重复的问答对")
    print("  ❌ 向量矩阵异常庞大（27308个向量）")
    print("  ❌ 向量索引与数据不匹配")
    print("  ❌ 查询时匹配到错误的向量")
    print()
    
    # 步骤1: 备份数据
    backup_path = backup_data()
    if not backup_path:
        print("❌ 备份失败，停止修复")
        return
    
    # 步骤2: 创建干净的数据
    if not create_clean_qa_data():
        print("❌ 创建数据失败")
        return
    
    # 步骤3: 验证数据
    verify_clean_data()
    
    print("=" * 60)
    print("🎉 修复完成！")
    print()
    print("📋 下一步操作:")
    print("   1. 重启服务器: python3 start.py")
    print("   2. 等待服务器完全启动（会自动重建向量索引）")
    print("   3. 运行相似度测试验证修复效果")
    print()
    print("💡 预期结果:")
    print("   - 每个分类只有1个唯一的问答对")
    print("   - 向量矩阵大小与数据量匹配（4个向量对应4个问答对）")
    print("   - 完全相同的问题相似度接近1.0")
    print("   - 查询匹配到正确的问题和分类")
    print("   - 使用了RAG系统的成熟向量处理逻辑")
    print()
    print(f"📁 数据备份位置: {backup_path}")


if __name__ == "__main__":
    main()
