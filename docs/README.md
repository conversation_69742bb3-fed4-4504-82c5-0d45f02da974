# GuiXiaoXiRag 文档中心

欢迎来到 GuiXiaoXiRag 文档中心！这里包含了项目的完整文档，帮助您快速上手和深入了解系统。

## 📚 文档导航

### 🚀 快速开始
- **[快速开始指南](Quick_Start_Guide.md)** - 5分钟快速启动服务
- **[项目README](../README.md)** - 项目概述和基本信息

### 📖 用户指南
- **[API文档](API_Documentation.md)** - 完整的API接口文档
- **[API测试示例](API_Testing_Examples.md)** - 详细的测试用例和示例

### 🏗️ 技术文档
- **[系统架构概览](Architecture_Overview.md)** - 系统设计和架构说明
- **[部署指南](Deployment_Guide.md)** - 生产环境部署指南

### 👨‍💻 开发者资源
- **[开发者指南](Developer_Guide.md)** - 开发环境设置和贡献指南

## 📋 文档概览

### 1. 快速开始指南
**适用人群**: 新用户、快速体验者
**内容**: 
- 环境要求和安装步骤
- 基础配置和启动方法
- 简单的使用示例
- 常见问题解决

**预计阅读时间**: 10分钟

### 2. API文档
**适用人群**: 开发者、集成者
**内容**:
- 完整的API端点说明
- 请求/响应格式
- 参数说明和示例
- 错误码和处理方法

**预计阅读时间**: 30分钟

### 3. API测试示例
**适用人群**: 测试人员、开发者
**内容**:
- 各种API的测试用例
- Python/JavaScript客户端示例
- 批量操作示例
- 性能测试方法

**预计阅读时间**: 20分钟

### 4. 系统架构概览
**适用人群**: 架构师、高级开发者
**内容**:
- 系统整体架构设计
- 模块职责和交互关系
- 技术选型说明
- 扩展性和性能考虑

**预计阅读时间**: 45分钟

### 5. 部署指南
**适用人群**: 运维人员、系统管理员
**内容**:
- 多种部署方式（直接部署、Docker、生产环境）
- 配置管理和优化
- 监控和日志管理
- 故障排除和维护

**预计阅读时间**: 60分钟

### 6. 开发者指南
**适用人群**: 贡献者、核心开发者
**内容**:
- 开发环境设置
- 代码规范和最佳实践
- 测试指南和调试技巧
- 贡献流程和代码审查

**预计阅读时间**: 90分钟

## 🎯 按角色推荐阅读路径

### 👤 普通用户
1. [快速开始指南](Quick_Start_Guide.md) - 了解基本使用
2. [API文档](API_Documentation.md) - 学习API使用
3. [API测试示例](API_Testing_Examples.md) - 参考实际示例

### 🔧 系统集成者
1. [API文档](API_Documentation.md) - 详细了解接口
2. [API测试示例](API_Testing_Examples.md) - 学习集成方法
3. [系统架构概览](Architecture_Overview.md) - 理解系统设计
4. [部署指南](Deployment_Guide.md) - 部署到目标环境

### 🏗️ 运维人员
1. [快速开始指南](Quick_Start_Guide.md) - 基础了解
2. [部署指南](Deployment_Guide.md) - 重点阅读
3. [系统架构概览](Architecture_Overview.md) - 理解系统结构

### 👨‍💻 开发者
1. [开发者指南](Developer_Guide.md) - 开发环境设置
2. [系统架构概览](Architecture_Overview.md) - 理解架构设计
3. [API文档](API_Documentation.md) - 了解接口设计
4. [API测试示例](API_Testing_Examples.md) - 学习测试方法

### 🏛️ 架构师
1. [系统架构概览](Architecture_Overview.md) - 重点阅读
2. [部署指南](Deployment_Guide.md) - 了解部署架构
3. [开发者指南](Developer_Guide.md) - 了解开发规范

## 📊 功能特性文档

### 核心功能
| 功能 | 文档位置 | 说明 |
|------|----------|------|
| 智能查询 | [API文档 - 查询API](API_Documentation.md#2-查询-api) | 多模式智能查询功能 |
| 文档管理 | [API文档 - 文档管理API](API_Documentation.md#3-文档管理-api) | 文档上传和处理 |
| 知识库管理 | [API文档 - 知识库管理API](API_Documentation.md#4-知识库管理-api) | 知识库创建和管理 |
| 知识图谱 | [API文档 - 知识图谱API](API_Documentation.md#5-知识图谱-api) | 图谱构建和查询 |
| 意图识别 | [API文档 - 意图识别API](API_Documentation.md#6-意图识别-api) | 查询意图分析 |
| 系统管理 | [API文档 - 系统管理API](API_Documentation.md#1-系统管理-api) | 系统监控和配置 |

### 高级特性
| 特性 | 文档位置 | 说明 |
|------|----------|------|
| 批量处理 | [API测试示例 - 批量操作](API_Testing_Examples.md#2-查询-api-测试) | 批量查询和文档处理 |
| 缓存管理 | [API文档 - 缓存管理API](API_Documentation.md#7-缓存管理-api) | 性能优化缓存 |
| 性能监控 | [部署指南 - 监控和日志](Deployment_Guide.md#监控和日志) | 系统性能监控 |
| 安全检查 | [API文档 - 意图识别API](API_Documentation.md#6-意图识别-api) | 内容安全检查 |

## 🔍 快速查找

### 常见任务
- **如何启动服务?** → [快速开始指南 - 快速安装](Quick_Start_Guide.md#快速安装)
- **如何上传文档?** → [API文档 - 文档管理](API_Documentation.md#3-文档管理-api)
- **如何执行查询?** → [API文档 - 查询API](API_Documentation.md#2-查询-api)
- **如何创建知识库?** → [API文档 - 知识库管理](API_Documentation.md#4-知识库管理-api)
- **如何部署到生产环境?** → [部署指南 - 生产环境部署](Deployment_Guide.md#方式三生产环境部署)

### 故障排除
- **服务启动失败** → [快速开始指南 - 常见问题](Quick_Start_Guide.md#常见问题)
- **查询没有结果** → [快速开始指南 - Q2](Quick_Start_Guide.md#q2-查询没有返回结果)
- **文件上传失败** → [快速开始指南 - Q3](Quick_Start_Guide.md#q3-文件上传失败)
- **内存使用过高** → [快速开始指南 - Q4](Quick_Start_Guide.md#q4-内存使用过高)
- **生产环境问题** → [部署指南 - 故障排除](Deployment_Guide.md#故障排除)

### 开发相关
- **如何贡献代码?** → [开发者指南 - 贡献指南](Developer_Guide.md#贡献指南)
- **代码规范是什么?** → [开发者指南 - 代码规范](Developer_Guide.md#代码规范)
- **如何运行测试?** → [开发者指南 - 测试指南](Developer_Guide.md#测试指南)
- **如何调试问题?** → [开发者指南 - 调试技巧](Developer_Guide.md#调试技巧)

## 📝 文档更新日志

### v2.0.0 (当前版本)
- ✅ 完整的API文档
- ✅ 详细的测试示例
- ✅ 系统架构说明
- ✅ 生产部署指南
- ✅ 开发者指南
- ✅ 快速开始指南

### 计划更新
- 🔄 用户界面使用指南
- 🔄 性能调优指南
- 🔄 安全配置指南
- 🔄 多语言客户端SDK
- 🔄 视频教程

## 🤝 文档贡献

我们欢迎社区贡献文档！如果您发现文档中的错误或希望改进内容，请：

1. **报告问题**: 在 GitHub Issues 中报告文档问题
2. **提交改进**: 通过 Pull Request 提交文档改进
3. **建议新内容**: 在 GitHub Discussions 中建议新的文档内容

### 文档贡献指南
- 使用清晰、简洁的语言
- 提供实际可运行的示例
- 保持格式一致性
- 添加适当的截图或图表
- 更新相关的索引和链接

## 📞 获取帮助

如果您在使用文档过程中遇到问题：

1. **搜索现有文档**: 使用浏览器搜索功能查找相关内容
2. **查看FAQ**: 检查各文档中的常见问题部分
3. **社区支持**: 在 GitHub Discussions 中提问
4. **问题反馈**: 在 GitHub Issues 中报告问题
5. **直接联系**: 通过邮件联系维护团队

## 📄 文档许可

本文档采用 [MIT 许可证](../LICENSE) 发布，与项目代码使用相同的许可证。

---

**感谢您使用 GuiXiaoXiRag！** 希望这些文档能帮助您更好地使用和理解系统。如有任何问题或建议，请随时联系我们。
